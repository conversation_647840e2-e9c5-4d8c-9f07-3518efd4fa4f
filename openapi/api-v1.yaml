openapi: 3.0.2

info:
  title: FE-EV-RCPT-TMPL-BFF
  version: 1.0.0

security:
  - BearerAuth: []

paths:
  /api/v1/templates:
    get:
      tags:
        - all-template-information
      operationId: GetTemplates
      summary: Retrieve All Receipts Template Information
      description: |
        This endpoint retrieves detailed information about all receipt templates available.
        This information includes logo, header, footer etc.
      responses:
        '200':
          description: template information fetched and return successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplatesResponse'
        default:
          description: Error response containing the status code and error message
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
  /api/v1/templates/{connection_id}/{template_id}:
    get:
      tags:
        - template-information
      operationId: GetTemplate
      summary: Retrieve Receipt Template Information
      description: |
        This endpoint retrieves detailed information about a specific receipt template by given template id and connection id
        The information includes logo, header, footer etc.
      parameters:
        - $ref: '#/components/parameters/TemplateID'
        - $ref: '#/components/parameters/ConnectionID'
      responses:
        '200':
          description: template information fetched and return successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TemplateResponse'
        '401':
          description: Unauthorized - Authentication failed or token is invalid
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        default:
          description: Error response containing the status code and error message
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
    put:
        tags:
          - update-template
        operationId: UpdateTemplate
        summary: Update existing template
        description: |
          This endpoint update existing template information into database
        parameters:
          - $ref: '#/components/parameters/TemplateID'
          - $ref: '#/components/parameters/ConnectionID'
        requestBody:
         content:
          application/json:
            schema:
              $ref: "#/components/schemas/TemplateResponse"
        responses:
          '204':
            description: existing template updated successfully
          '400':
            description: bad request
          '401':
            description: Unauthorized - Authentication failed or token is invalid
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ErrorResponse'
          '409':
            description: conflict template already exist with same information
          default:
            description: Error response containing the status code and error message
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ErrorResponse'
    delete:
        tags:
          - delete-template
        operationId: DeleteTemplate
        summary: Delete existing template
        description: |
          This endpoint will delete existing template
        parameters:
          - $ref: '#/components/parameters/TemplateID'
          - $ref: '#/components/parameters/ConnectionID'
        responses:
          '204':
            description: existing template deleted successfully
          '401':
            description: Unauthorized - Authentication failed or token is invalid
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ErrorResponse'
          default:
            description: Error response containing the status code and error message
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ErrorResponse'
  /api/v1/templates/{connection_id}:
    post:
      tags:
          - new-template
      operationId: NewTemplate
      summary: Add new template
      description: |
          This endpoint create a new template and save it into database
      parameters:
          - $ref: '#/components/parameters/ConnectionID'
      requestBody:
         content:
          application/json:
            schema:
              $ref: "#/components/schemas/TemplateResponse"
      responses:
          '201':
            description: new template created successfully
          '400':
            description: bad request
          '401':
            description: Unauthorized - Authentication failed or token is invalid
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ErrorResponse'
          default:
            description: Error response containing the status code and error message
            content:
              application/json:
                schema:
                  $ref: '#/components/schemas/ErrorResponse'
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
  parameters:
    AuthorizationHeader:
      name: authorization
      in: header
      required: true
      schema:
        type: string
    TemplateID:
      name: template_id
      in: path
      required: true
      schema:
        type: string
        minLength: 36
        maxLength: 36
        description: Unique id of the new or updated receipt template object.
    ConnectionID:
      name: connection_id
      in: path
      required: true
      schema:
        type: string
        minLength: 36
        maxLength: 36
        description: Unique connection id of party for which service is requesting receipt template.
  schemas:
    TemplatesResponse:
      type: array
      items:
        $ref: "#/components/schemas/TemplateResponse"
    TemplateResponse:
      type: object
      properties:
        template_id:
          type: string
          minLength: 36
          maxLength: 36
        connection_id:
          type: string
          minLength: 36
          maxLength: 36
        is_default:
          type: boolean
          default: false
        header:
          $ref: '#/components/schemas/Header'
        body:
          $ref: '#/components/schemas/Body'
        footer:
          $ref: '#/components/schemas/Footer'
        connection:
          $ref: '#/components/schemas/Connection'
        last_updated:
          type: string
          format: date-time
    Header:
      type: object
      properties:
        header_text:
          $ref: '#/components/schemas/Text'
        header_icon:
          $ref: '#/components/schemas/Icon'
    Footer:
      type: object
      properties:
        footer_text:
           $ref: '#/components/schemas/Text'
        footer_icon:
          $ref: '#/components/schemas/Icon'
    Body:
      type: string
    Text:
      type: object
      properties:
        font:
          type: string
        style:
          type: string
        color:
          type: string
        value:
          type: string
    Icon:
      type: object
      properties:
        src:
         type: string
        width:
         type: integer
        height:
          type: integer
        name:
         type: string
    Connection:
      type: object
      properties:
        connection_name:
          type: string
          x-go-type-skip-optional-pointer: true
        country_code:
          type: string
          x-go-type-skip-optional-pointer: true
        party_code:
          type: string
          x-go-type-skip-optional-pointer: true
        cpo_url:
          type: string
          x-go-type-skip-optional-pointer: true
    ErrorResponse:
      type: object
      properties:
        status_code:
          type: integer
        status_message:
          type: string
          description: A description of the error.
          example: "An unexpected error occurred."
        timestamp:
          type: string
          format: date-time
          description: The time this message was generated.
          example: "2023-10-29T12:00:00Z"
