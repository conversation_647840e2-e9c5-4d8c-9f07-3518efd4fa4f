{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
    {
      "type": "go",
      "request": "launch",
      "name": "Launch Program",
      "program": "${workspaceFolder}/${input:mainFile}",
      "preLaunchTask": "run-docker-compose"
    }
    ],
      "inputs": [
    {
      "type": "promptString",
      "id": "mainFile",
      "description": "Enter the path to the main Go file (e.g., main.go)"
    }
  ]
}